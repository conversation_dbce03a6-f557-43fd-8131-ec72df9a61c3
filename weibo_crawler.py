import requests
import json
import time
import random
from datetime import datetime


class WeiboCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'Host': 'weibo.com',
            'Connection': 'keep-alive',
            'sec-ch-ua-platform': '"Windows"',
            'X-XSRF-TOKEN': 'O-uM8yd-67MwfOaAslj2eHMz',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'server-version': 'v2025.07.22.1',
            'sec-ch-ua-mobile': '?0',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'client-version': 'v2.47.92',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://weibo.com/u/2109911562',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cookie': 'XSRF-TOKEN=O-uM8yd-67MwfOaAslj2eHMz; SCF=AuGxIxJmQnMMIbTmkG9-nZU9T-AoIhwcQZ45V52k1CsxnFQq0U6X5MVGw4kVvaNcoPc3wd0ehh3jRE7EvB1QgjI.; _s_tentry=passport.weibo.com; Apache=2263394327363.6636.1753169601286; SINAGLOBAL=2263394327363.6636.1753169601286; ULV=1753169601316:1:1:1:2263394327363.6636.1753169601286:; ALF=1755766134; SUB=_2A25FeyAmDeRhGe5O6lYZ-C3JzziIHXVm-T3urDV8PUJbkNAbLWnjkW1NdZRa9UPluW5Xec86tbbrhlBV3DihzlM-; SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9WFxiOys_jC.wD3Nb31TZ3y95JpX5KzhUgL.Fon7eKBR1hefShB2dJLoI7vQUg8bqgLJ9g4rP0241hzt; WBPSESS=Dt2hbAUaXfkVprjyrAZT_GJrhXrWgOGOlWZOlE4IMJaB_I0LmHtCf2R5iC3nsq9alZl7iZF7lHbVKb79dd8umIKPxFEqww3JyhmIcJUHDATA54Fj8R4ptcyPQ7qpz3RW5xTOKjp2HGao8kn7SMYYHjPi_VR3QUFlPoLBWx2t2KjpdggNLviRZQJtMbes1sA0oMr7aUpxGLfbEtGSZBwa_Q=='
        }
        self.session.headers.update(self.headers)
        
        # 禁用SSL验证和警告
        self.session.verify = False
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    def format_weibo_time(self, weibo_time_str):
        """
        将微博时间格式转换为标准格式
        输入格式: "Sat Jul 19 09:09:56 +0800 2025"
        输出格式: "2025-07-19 09:09:56"
        """
        try:
            # 解析微博时间格式
            # 格式: "Sat Jul 19 09:09:56 +0800 2025"
            dt = datetime.strptime(weibo_time_str, "%a %b %d %H:%M:%S %z %Y")
            # 转换为标准格式
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except Exception as e:
            print(f"时间格式转换失败: {weibo_time_str}, 错误: {e}")
            return weibo_time_str  # 如果转换失败，返回原始时间
    
    def get_user_posts(self, user_id, keyword=None, max_pages=3):
        """
        获取用户微博并搜索关键词
        :param user_id: 微博用户ID
        :param keyword: 搜索关键词
        :param max_pages: 最大页数
        """
        posts = []
        
        for page in range(1, max_pages + 1):
            # 使用抓包中的真实API
            url = "https://weibo.com/ajax/statuses/searchProfile"
            params = {
                'uid': user_id,
                'page': page,
                'q': keyword if keyword else ''
            }
            
            try:
                response = self.session.get(url, params=params, timeout=10)
                
                if response.status_code != 200:
                    print(f"请求失败，状态码: {response.status_code}")
                    break
                
                data = response.json()
                
                # 检查响应数据结构
                if not data.get('ok'):
                    print(f"API返回错误: {data}")
                    break
                
                # 解析微博数据
                statuses = data.get('data', {}).get('list', [])
                if not statuses:
                    print(f"第{page}页无数据")
                    break
                
                found_count = 0
                for status in statuses:
                    # 提取微博文本
                    text_raw = status.get('text_raw', '')
                    text = status.get('text', '')
                    
                    # 如果有关键词过滤，检查是否包含关键词
                    if keyword and keyword not in text_raw:
                        continue
                    
                    found_count += 1
                    original_time = status.get('created_at')
                    formatted_time = self.format_weibo_time(original_time) if original_time else None

                    # 构建微博URL
                    bid = status.get('bid', '')
                    if bid:
                        weibo_url = f"https://weibo.com/{user_id}/{bid}"
                    else:
                        # 如果没有bid，使用mid构建URL
                        mid = status.get('mid', '')
                        weibo_url = f"https://weibo.com/detail/{mid}" if mid else f"https://weibo.com/u/{user_id}"

                    post = {
                        'text': text_raw,
                        'text_html': text,
                        'created_at': original_time,
                        'formatted_time': formatted_time,
                        'reposts': status.get('reposts_count', 0),
                        'comments': status.get('comments_count', 0),
                        'likes': status.get('attitudes_count', 0),
                        'mid': status.get('mid'),
                        'id': status.get('id'),
                        'bid': bid,
                        'url': weibo_url
                    }
                    posts.append(post)
                
                print(f"已获取第{page}页微博，找到{found_count}条相关微博")
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                print(f"获取第{page}页时出错: {e}")
                break
                
        return posts
    
    def save_results(self, posts, filename):
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(posts, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到 {filename}")

# 使用示例
if __name__ == "__main__":
    crawler = WeiboCrawler()
    user_id = "2109911562"  # 目标用户ID
    keyword = "权威发布"      # 搜索关键词
    
    posts = crawler.get_user_posts(user_id, keyword, max_pages=1)
    print(f"共获取到 {len(posts)} 条相关微博")
    
    # 显示部分结果
    for i, post in enumerate(posts[:5]):
        print(f"From:weibo_杭州消防【{i+1}】")
        print(f"发布时间: {post['formatted_time']}")
        print(f"发布内容: {post['text'][:150]}")
        print(f"微博链接: {post['url']}")
        print("-" * 50)
    
    # 保存结果
    crawler.save_results(posts, f"weibo_{user_id}_{keyword}.json")



