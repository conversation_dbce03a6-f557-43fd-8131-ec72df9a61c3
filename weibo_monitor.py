import requests
import json
import time
import random
import re
from datetime import datetime
import os


class WeiboCrawler:
    def __init__(self, success_file="success_posts.json", failed_file="failed_posts.json"):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            'Accept': 'application/json, text/plain, */*'
        })
        self.session.verify = False
        
        # 分别管理成功和失败的记录文件
        self.success_file = success_file  # 发送成功的记录
        self.failed_file = failed_file    # 发送失败的记录
        
        self.success_posts = self.load_posts_from_file(self.success_file)  # 成功记录（set格式）
        self.failed_posts = self.load_posts_from_file(self.failed_file)    # 失败记录（dict格式：URL -> 消息体）
        
        # 确保失败记录是字典格式
        if isinstance(self.failed_posts, set):
            self.failed_posts = {}  # 如果是旧格式，重置为空字典
        
        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings()
    
    def load_posts_from_file(self, filename):
        """从文件加载微博记录"""
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return set(data)  # 兼容旧格式（只有URL的列表）
                    elif isinstance(data, dict):
                        return data  # 新格式（URL -> 消息体的字典）
                    else:
                        return {}
            except:
                return {}
        return {}
    
    def save_posts_to_file(self, posts_data, filename):
        """保存微博记录到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            if isinstance(posts_data, set):
                json.dump(list(posts_data), f, ensure_ascii=False, indent=2)
            else:
                json.dump(posts_data, f, ensure_ascii=False, indent=2)
    
    def add_to_success(self, post_id):
        """添加到成功记录"""
        # 如果传入的是字典，提取URL；如果是字符串，直接使用
        if isinstance(post_id, dict):
            url = post_id['url']
        else:
            url = post_id

        self.success_posts.add(url)
        # 从失败记录中移除（如果存在）
        self.failed_posts.pop(url, None)
        self.save_posts_to_file(self.success_posts, self.success_file)
        self.save_posts_to_file(self.failed_posts, self.failed_file)
    
    def add_to_failed(self, post_id, message):
        """添加到失败记录（保存完整消息体）"""
        # 只有不在成功记录中的才添加到失败记录
        if post_id not in self.success_posts:
            self.failed_posts[post_id] = message  # 保存完整的消息体
            self.save_posts_to_file(self.failed_posts, self.failed_file)
    
    def get_post_id(self, post):
        """生成微博的唯一ID"""
        # 使用URL作为唯一标识
        return post['url']
    
    def xiaowang(self, content):
        """发送微信消息"""
        url = 'http://10.70.230.46:5003/robot_mothod/api/robot'
        data = {
            'wxid': 'huabaifeng',
            'data_type': 'Text',
            'data': content
        }
        
        try:
            res = requests.post(url, data=data, timeout=10)
            result = res.json()
            print(f"微信API返回: {result}")
            
            # 检查发送是否成功
            # 根据您提供的返回格式：{'code': 0, 'message': 'Text', 'data': '发送成功'}
            if result.get('code') == 0 and '发送成功' in result.get('data', ''):
                return True
            else:
                return False
        except Exception as e:
            print(f"微信发送失败: {e}")
            return False
    
    def format_time(self, time_str):
        """将微博时间格式转换为标准格式"""
        try:
            # 微博时间格式: "Sat Jul 19 09:09:56 +0800 2025"
            dt = datetime.strptime(time_str, "%a %b %d %H:%M:%S %z %Y")
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return time_str  # 如果转换失败，返回原始时间
    
    def retry_failed_posts(self):
        """重发失败的微博（使用保存的消息体）"""
        if not self.failed_posts:
            print("📝 没有失败的微博需要重发")
            return

        print(f"🔄 发现{len(self.failed_posts)}条失败的微博，开始重发...")

        # 将失败记录转换为临时列表，避免在迭代时修改字典
        failed_items = list(self.failed_posts.items())

        for post_url, saved_message in failed_items:
            print(f"\n🔄 重发失败微博: {post_url}")
            print(f"消息预览: {saved_message[:100]}...")

            if self.xiaowang(saved_message):
                print(f"✅ 重发成功")
                self.add_to_success(post_url)
            else:
                print(f"❌ 重发仍然失败")

            time.sleep(2)
    
    def get_posts(self, user_id="2109911562", keyword=None, pages=3, per_page=None):
        """
        获取微博
        user_id: 用户ID (默认杭州消防)
        keyword: 关键词过滤
        pages: 页数
        per_page: 每页数量限制
        """
        all_posts = []
        
        for page in range(1, pages + 1):
            print(f"获取第{page}页...")
            
            url = "https://m.weibo.cn/api/container/getIndex"
            params = {
                'containerid': f'107603{user_id}',
                'page': page
            }
            
            try:
                response = self.session.get(url, params=params, timeout=10)
                data = response.json()
                
                if data.get('ok') != 1:
                    break
                
                cards = data.get('data', {}).get('cards', [])
                page_posts = []
                
                for card in cards:
                    if card.get('card_type') != 9:
                        continue
                    
                    mblog = card.get('mblog', {})
                    if not mblog:
                        continue
                    
                    # 提取文本
                    text = mblog.get('text_raw', '') or re.sub(r'<[^>]+>', '', mblog.get('text', ''))
                    
                    # 关键词过滤
                    if keyword and keyword not in text:
                        continue
                    
                    # 构建数据
                    original_time = mblog.get('created_at', '')
                    formatted_time = self.format_time(original_time)
                    
                    post = {
                        'text': text,
                        'time': formatted_time,
                        'original_time': original_time,  # 保留原始时间
                        'url': f"https://weibo.com/{user_id}/{mblog.get('bid', '')}"
                    }
                    page_posts.append(post)
                    
                    # 每页数量限制
                    if per_page and len(page_posts) >= per_page:
                        break
                
                all_posts.extend(page_posts)
                print(f"第{page}页: {len(page_posts)}条")
                
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                print(f"第{page}页出错: {e}")
                break
        
        return all_posts
    
    def send_to_wechat(self, posts):
        """发送微博内容到微信（新逻辑）"""
        if not posts:
            print("未找到符合条件的微博内容")
            return
        
        new_posts = []  # 新的未发送成功过的微博
        
        # 过滤出未在成功记录中的微博
        for post in posts:
            post_id = self.get_post_id(post)
            if post_id not in self.success_posts:
                new_posts.append(post)
        
        if not new_posts:
            print("所有微博都已发送成功过，无新内容")
            return
        
        print(f"发现{len(new_posts)}条需要发送的微博，开始发送...")
        
        success_count = 0
        failed_count = 0
        
        for i, post in enumerate(new_posts):
            post_id = self.get_post_id(post)
            
            # 构建微信消息内容
            message = f"""📢 来源：微博【杭州消防】
⏰ 发布时间：{post['time']}
📝 发布内容：{post['text']}
🔗 URL：{post['url']}"""
            
            print(f"\n正在发送第{i+1}条微博...")
            print(f"微博ID: {post_id}")
            print(message)
            print("-" * 50)
            
            # 发送到微信
            if self.xiaowang(message):
                print(f"✅ 第{i+1}条微博发送成功")
                self.add_to_success(post_id)
                success_count += 1
            else:
                print(f"❌ 第{i+1}条微博发送失败")
                self.add_to_failed(post_id, message)  # 保存完整的消息体
                failed_count += 1
            
            # 避免发送过快，添加延时
            time.sleep(2)
        
        print(f"\n📊 发送完成: 成功{success_count}条, 失败{failed_count}条")
    
    def save(self, posts, filename):
        """保存到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(posts, f, ensure_ascii=False, indent=2)
        print(f"保存到: {filename}")
    
    def show(self, posts, count=5):
        """显示微博"""
        for post in posts[:count]:
            print(f"来源：微博【杭州消防】")
            print(f"发布时间：{post['time']}")
            print(f"发布内容：{post['text'][:100]}...")
            print(f"URL：{post['url']}")


def monitor_weibo(interval_minutes=1, keyword="【权威发布】", pages=3):
    """
    持续监控微博
    :param interval_minutes: 监控间隔（分钟）
    :param keyword: 关键词
    :param pages: 获取页数
    """
    crawler = WeiboCrawler()
    
    print(f"🚀 开始监控杭州消防微博")
    print(f"📋 监控设置:")
    print(f"   - 关键词: {keyword}")
    print(f"   - 获取页数: {pages}")
    print(f"   - 监控间隔: {interval_minutes}分钟")
    print(f"   - 成功记录文件: {crawler.success_file}")
    print(f"   - 失败记录文件: {crawler.failed_file}")
    print(f"   - 当前成功记录: {len(crawler.success_posts)}条")
    print(f"   - 当前失败记录: {len(crawler.failed_posts)}条")
    print("-" * 60)
    
    # 启动时先重发失败的微博
    print(f"\n🔄 检查并重发失败的微博...")
    crawler.retry_failed_posts()
    
    round_count = 0
    
    try:
        while True:
            round_count += 1
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n🔍 第{round_count}轮监控 - {current_time}")
            
            try:
                # 获取微博
                posts = crawler.get_posts(
                    pages=pages,
                    per_page=None,
                    keyword=keyword
                )
                
                print(f"📊 本轮获取到 {len(posts)} 条符合条件的微博")
                
                # 发送到微信（自动去重）
                crawler.send_to_wechat(posts)
                
            except Exception as e:
                print(f"❌ 本轮监控出错: {e}")
            
            # 等待下一轮
            print(f"\n⏰ 等待{interval_minutes}分钟后进行下一轮监控...")
            print("=" * 60)
            time.sleep(interval_minutes * 60)
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 监控已停止")
        print(f"📊 总共执行了 {round_count} 轮监控")
        print(f"📝 已发送微博记录: {len(crawler.success_posts)} 条")


if __name__ == "__main__":
    # ========== 配置参数（在这里修改） ==========
    RUN_MODE = "monitor"  # "test" = 单次测试, "monitor" = 持续监控
    INTERVAL_MINUTES = 1  # 监控间隔（分钟）
    KEYWORD = "【权威发布】"  # 关键词
    PAGES = 3  # 获取页数
    # ==========================================
    
    if RUN_MODE == "test":
        # 单次执行模式
        print("🧪 单次执行模式")
        crawler = WeiboCrawler()
        posts = crawler.get_posts(pages=PAGES, keyword=KEYWORD)
        print(f"\n总共获取: {len(posts)}条")
        crawler.show(posts)
        crawler.send_to_wechat(posts)
        
    elif RUN_MODE == "monitor":
        # 持续监控模式
        print("🔄 持续监控模式")
        monitor_weibo(
            interval_minutes=INTERVAL_MINUTES,
            keyword=KEYWORD,
            pages=PAGES
        )
    else:
        print("❌ 配置错误：RUN_MODE 应该是 'test' 或 'monitor'")
