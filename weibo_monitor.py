import requests
import json
import time
import random
import re
from datetime import datetime
import sqlite3


class WeiboCrawler:
    def __init__(self, db_file="weibo_monitor.db"):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            'Accept': 'application/json, text/plain, */*'
        })
        self.session.verify = False

        # 数据库文件
        self.db_file = db_file

        # 初始化数据库
        self.init_database()

        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings()

    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 创建文章信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS articles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT UNIQUE NOT NULL,
                title TEXT,
                content TEXT NOT NULL,
                publish_time TEXT NOT NULL,
                original_time TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建发送记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS send_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                article_url TEXT NOT NULL,
                status TEXT NOT NULL,  -- 'success' 或 'failed'
                message_body TEXT,     -- 发送的完整消息体
                error_info TEXT,       -- 失败时的错误信息
                send_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (article_url) REFERENCES articles (url)
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_articles_url ON articles (url)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_send_records_url ON send_records (article_url)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_send_records_status ON send_records (status)')

        conn.commit()
        conn.close()
        print(f"📊 数据库初始化完成: {self.db_file}")

    def save_article(self, url, title, content, publish_time, original_time):
        """保存文章信息到数据库（如果不存在才插入，避免更新id）"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT OR IGNORE INTO articles
                (url, title, content, publish_time, original_time)
                VALUES (?, ?, ?, ?, ?)
            ''', (url, title, content, publish_time, original_time))
            conn.commit()
        except Exception as e:
            print(f"❌ 保存文章失败: {e}")
        finally:
            conn.close()

    def is_article_sent_successfully(self, url):
        """检查文章是否已成功发送过"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT COUNT(*) FROM send_records
            WHERE article_url = ? AND status = 'success'
        ''', (url,))

        count = cursor.fetchone()[0]
        conn.close()
        return count > 0

    def get_failed_posts(self):
        """获取发送失败的文章列表"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 获取最新的失败记录（排除已成功发送的）
        cursor.execute('''
            SELECT DISTINCT sr.article_url, sr.message_body
            FROM send_records sr
            WHERE sr.status = 'failed'
            AND sr.article_url NOT IN (
                SELECT article_url FROM send_records
                WHERE status = 'success'
            )
            ORDER BY sr.send_time DESC
        ''')

        failed_posts = {}
        for row in cursor.fetchall():
            url, message_body = row
            failed_posts[url] = message_body

        conn.close()
        return failed_posts
    
    def add_to_success(self, post_id, message_body=""):
        """添加到成功记录"""
        # 如果传入的是字典，提取URL；如果是字符串，直接使用
        if isinstance(post_id, dict):
            url = post_id['url']
        else:
            url = post_id

        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO send_records
                (article_url, status, message_body)
                VALUES (?, 'success', ?)
            ''', (url, message_body))
            conn.commit()
            print(f"✅ 成功记录已保存: {url}")
        except Exception as e:
            print(f"❌ 保存成功记录失败: {e}")
        finally:
            conn.close()

    def update_failed_to_success(self, post_url):
        """将失败记录更新为成功状态"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE send_records
                SET status = 'success', send_time = CURRENT_TIMESTAMP
                WHERE article_url = ? AND status = 'failed'
            ''', (post_url,))
            conn.commit()
            print(f"✅ 失败记录已更新为成功: {post_url}")
        except Exception as e:
            print(f"❌ 更新记录失败: {e}")
        finally:
            conn.close()

    def add_to_failed(self, post_id, message, error_info=""):
        """添加到失败记录（保存完整消息体）"""
        # 如果传入的是字典，提取URL；如果是字符串，直接使用
        if isinstance(post_id, dict):
            url = post_id['url']
        else:
            url = post_id

        # 只有不在成功记录中的才添加到失败记录
        if not self.is_article_sent_successfully(url):
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            try:
                cursor.execute('''
                    INSERT INTO send_records
                    (article_url, status, message_body, error_info)
                    VALUES (?, 'failed', ?, ?)
                ''', (url, message, error_info))
                conn.commit()
                print(f"❌ 失败记录已保存: {url}")
            except Exception as e:
                print(f"❌ 保存失败记录失败: {e}")
            finally:
                conn.close()
    
    def get_post_id(self, post):
        """生成微博的唯一ID"""
        # 使用URL作为唯一标识
        return post['url']
    
    def xiaowang(self, content):
        """发送微信消息"""
        url = 'http://10.70.230.46:5003/robot_mothod/api/robot'
        data = {
            'wxid': 'huabaifeng',
            'data_type': 'Text',
            'data': content
        }
        
        try:
            res = requests.post(url, data=data, timeout=10)
            result = res.json()
            print(f"微信API返回: {result}")
            
            # 检查发送是否成功
            # 根据您提供的返回格式：{'code': 0, 'message': 'Text', 'data': '发送成功'}
            if result.get('code') == 0 and '发送成功' in result.get('data', ''):
                return True
            else:
                return False
        except Exception as e:
            print(f"微信发送失败: {e}")
            return False
    
    def format_time(self, time_str):
        """将微博时间格式转换为标准格式"""
        try:
            # 微博时间格式: "Sat Jul 19 09:09:56 +0800 2025"
            dt = datetime.strptime(time_str, "%a %b %d %H:%M:%S %z %Y")
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return time_str  # 如果转换失败，返回原始时间
    
    def retry_failed_posts(self):
        """重发失败的微博（使用保存的消息体）"""
        failed_posts = self.get_failed_posts()

        if not failed_posts:
            print("📝 没有失败的微博需要重发")
            return

        print(f"🔄 发现{len(failed_posts)}条失败的微博，开始重发...")

        for post_url, saved_message in failed_posts.items():
            print(f"\n🔄 重发失败微博: {post_url}")
            print(f"消息预览: {saved_message[:100]}...")

            if self.xiaowang(saved_message):
                print(f"✅ 重发成功")
                self.update_failed_to_success(post_url)
            else:
                print(f"❌ 重发仍然失败")
                self.add_to_failed(post_url, saved_message, "重发失败")

            time.sleep(2)
    
    def get_posts(self, user_id="2109911562", keyword=None, pages=3, per_page=None):
        """
        获取微博
        user_id: 用户ID (默认杭州消防)
        keyword: 关键词过滤
        pages: 页数
        per_page: 每页数量限制
        """
        all_posts = []
        
        for page in range(1, pages + 1):
            print(f"获取第{page}页...")
            
            url = "https://m.weibo.cn/api/container/getIndex"
            params = {
                'containerid': f'107603{user_id}',
                'page': page
            }
            
            try:
                response = self.session.get(url, params=params, timeout=10)
                data = response.json()
                
                if data.get('ok') != 1:
                    break
                
                cards = data.get('data', {}).get('cards', [])
                page_posts = []
                
                for card in cards:
                    if card.get('card_type') != 9:
                        continue
                    
                    mblog = card.get('mblog', {})
                    if not mblog:
                        continue
                    
                    # 提取文本
                    text = mblog.get('text_raw', '') or re.sub(r'<[^>]+>', '', mblog.get('text', ''))
                    
                    # 关键词过滤
                    if keyword and keyword not in text:
                        continue
                    
                    # 构建数据
                    original_time = mblog.get('created_at', '')
                    formatted_time = self.format_time(original_time)
                    
                    post = {
                        'text': text,
                        'time': formatted_time,
                        'original_time': original_time,  # 保留原始时间
                        'url': f"https://weibo.com/{user_id}/{mblog.get('bid', '')}"
                    }
                    page_posts.append(post)
                    
                    # 每页数量限制
                    if per_page and len(page_posts) >= per_page:
                        break
                
                all_posts.extend(page_posts)
                print(f"第{page}页: {len(page_posts)}条")
                
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                print(f"第{page}页出错: {e}")
                break
        
        return all_posts
    
    def send_to_wechat(self, posts):
        """发送微博内容到微信（新逻辑）"""
        if not posts:
            print("未找到符合条件的微博内容")
            return

        new_posts = []  # 新的未发送成功过的微博

        # 过滤出未在成功记录中的微博（文章信息已在外部保存）
        for post in posts:
            post_id = self.get_post_id(post)

            # 检查是否已成功发送过
            if not self.is_article_sent_successfully(post_id):
                new_posts.append(post)

        if not new_posts:
            print("所有微博都已发送成功过，无新内容")
            return

        print(f"发现{len(new_posts)}条需要发送的微博，开始发送...")

        success_count = 0
        failed_count = 0

        for i, post in enumerate(new_posts):
            post_id = self.get_post_id(post)

            # 构建微信消息内容
            message = f"""📢 来源：微博【杭州消防】
⏰ 发布时间：{post['time']}
📝 发布内容：{post['text']}
🔗 URL：{post['url']}"""

            print(f"\n正在发送第{i+1}条微博...")
            print(f"微博ID: {post_id}")
            print(message)
            print("-" * 50)

            # 发送到微信
            send_result = self.xiaowang(message)
            if send_result:
                print(f"✅ 第{i+1}条微博发送成功")
                self.add_to_success(post_id, message)
                success_count += 1
            else:
                print(f"❌ 第{i+1}条微博发送失败")
                self.add_to_failed(post_id, message, "微信发送失败")
                failed_count += 1

            # 避免发送过快，添加延时
            time.sleep(2)

        print(f"\n📊 发送完成: 成功{success_count}条, 失败{failed_count}条")
    
    def save(self, posts, filename):
        """保存到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(posts, f, ensure_ascii=False, indent=2)
        print(f"保存到: {filename}")
    
    def show(self, posts, count=5):
        """显示微博"""
        for post in posts[:count]:
            print(f"来源：微博【杭州消防】")
            print(f"发布时间：{post['time']}")
            print(f"发布内容：{post['text'][:100]}...")
            print(f"URL：{post['url']}")


def get_database_stats(db_file):
    """获取数据库统计信息"""
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()

    # 获取文章总数
    cursor.execute('SELECT COUNT(*) FROM articles')
    total_articles = cursor.fetchone()[0]

    # 获取成功发送数
    cursor.execute('SELECT COUNT(DISTINCT article_url) FROM send_records WHERE status = "success"')
    success_count = cursor.fetchone()[0]

    # 获取失败发送数（排除已成功的）
    cursor.execute('''
        SELECT COUNT(DISTINCT article_url) FROM send_records
        WHERE status = "failed"
        AND article_url NOT IN (
            SELECT article_url FROM send_records WHERE status = "success"
        )
    ''')
    failed_count = cursor.fetchone()[0]

    conn.close()
    return total_articles, success_count, failed_count

def monitor_weibo(interval_minutes=1, keyword="【权威发布】", pages=3):
    """
    持续监控微博
    :param interval_minutes: 监控间隔（分钟）
    :param keyword: 关键词
    :param pages: 获取页数
    """
    crawler = WeiboCrawler()

    # 获取数据库统计信息
    total_articles, success_count, failed_count = get_database_stats(crawler.db_file)

    print(f"🚀 开始监控杭州消防微博")
    print(f"📋 监控设置:")
    print(f"   - 关键词: {keyword}")
    print(f"   - 获取页数: {pages}")
    print(f"   - 监控间隔: {interval_minutes}分钟")
    print(f"   - 数据库文件: {crawler.db_file}")
    print(f"   - 文章总数: {total_articles}条")
    print(f"   - 成功发送: {success_count}条")
    print(f"   - 失败待重发: {failed_count}条")
    print("-" * 60)
    
    # 启动时先重发失败的微博
    print(f"\n🔄 检查并重发失败的微博...")
    crawler.retry_failed_posts()
    
    round_count = 0
    
    try:
        while True:
            round_count += 1
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n🔍 第{round_count}轮监控 - {current_time}")
            
            try:
                # 获取杭州消防的所有微博（不过滤关键词）
                all_posts = crawler.get_posts(
                    pages=pages,
                    per_page=None,
                    keyword=None  # 不过滤，获取所有文章
                )

                print(f"📊 本轮获取到 {len(all_posts)} 条杭州消防微博")

                # 保存所有文章到数据库
                for post in all_posts:
                    crawler.save_article(
                        url=post['url'],
                        title="",  # 微博没有标题
                        content=post['text'],
                        publish_time=post['time'],
                        original_time=post['original_time']
                    )

                # 过滤出包含关键词的文章用于发送微信
                filtered_posts = [post for post in all_posts if keyword in post['text']]
                print(f"📊 其中包含'{keyword}'的微博: {len(filtered_posts)} 条")

                # 发送到微信（自动去重）
                crawler.send_to_wechat(filtered_posts)
                
            except Exception as e:
                print(f"❌ 本轮监控出错: {e}")
            
            # 等待下一轮
            print(f"\n⏰ 等待{interval_minutes}分钟后进行下一轮监控...")
            print("=" * 60)
            time.sleep(interval_minutes * 60)
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 监控已停止")
        print(f"📊 总共执行了 {round_count} 轮监控")

        # 获取最终统计信息
        total_articles, success_count, failed_count = get_database_stats(crawler.db_file)
        print(f"📝 最终统计:")
        print(f"   - 文章总数: {total_articles}条")
        print(f"   - 成功发送: {success_count}条")
        print(f"   - 失败待重发: {failed_count}条")


if __name__ == "__main__":
    # ========== 配置参数（在这里修改） ==========
    INTERVAL_MINUTES = 0.5  # 监控间隔（分钟，如果想单次执行可以设置很大的数字如9999）
    KEYWORD = "【权威发布】"  # 关键词
    PAGES = 3  # 获取页数
    # ==========================================

    monitor_weibo(
        interval_minutes=INTERVAL_MINUTES,
        keyword=KEYWORD,
        pages=PAGES
    )
